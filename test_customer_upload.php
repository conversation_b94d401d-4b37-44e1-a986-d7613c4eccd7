<?php
// 简化的客服图片上传测试
header('Content-Type: application/json; charset=utf-8');

// 开启错误报告但不显示在页面上
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

$result = ['code' => 0, 'msg' => '未知错误'];

try {
    // 检查是否有文件上传
    if (!isset($_FILES['image'])) {
        throw new Exception('没有接收到图片文件');
    }
    
    $file = $_FILES['image'];
    
    // 检查上传错误
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $error_messages = [
            UPLOAD_ERR_INI_SIZE => '文件大小超过了 php.ini 中 upload_max_filesize 选项限制的值',
            UPLOAD_ERR_FORM_SIZE => '文件大小超过了 HTML 表单中 MAX_FILE_SIZE 选项指定的值',
            UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
            UPLOAD_ERR_NO_FILE => '没有文件被上传',
            UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
            UPLOAD_ERR_CANT_WRITE => '文件写入失败',
        ];
        
        $error_msg = isset($error_messages[$file['error']]) ? 
                    $error_messages[$file['error']] : 
                    '上传错误代码：' . $file['error'];
        throw new Exception($error_msg);
    }
    
    // 检查文件类型
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('不支持的图片格式：' . $file['type']);
    }
    
    // 检查文件大小
    if ($file['size'] > 5 * 1024 * 1024) { // 5MB
        throw new Exception('图片大小不能超过5MB，当前大小：' . round($file['size'] / 1024 / 1024, 2) . 'MB');
    }
    
    // 创建上传目录
    $uploadDir = 'uploads/customer/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0777, true)) {
            throw new Exception('无法创建上传目录：' . $uploadDir);
        }
    }
    
    // 检查目录权限
    if (!is_writable($uploadDir)) {
        throw new Exception('上传目录不可写：' . $uploadDir);
    }
    
    // 生成文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = date('YmdHis') . '_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // 移动文件
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('文件保存失败');
    }
    
    // 验证文件是否成功保存
    if (!file_exists($filepath)) {
        throw new Exception('文件保存后验证失败');
    }
    
    $result = [
        'code' => 1,
        'msg' => '上传成功',
        'data' => [
            'image_url' => '/' . $filepath,
            'filename' => $filename
        ]
    ];
    
} catch (Exception $e) {
    $result = [
        'code' => 0,
        'msg' => $e->getMessage()
    ];
}

// 确保输出纯JSON
echo json_encode($result, JSON_UNESCAPED_UNICODE);
exit;
?>
