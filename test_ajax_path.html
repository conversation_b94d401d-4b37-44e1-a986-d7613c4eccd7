<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX路径测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-item {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>AJAX路径测试</h1>
        
        <div class="test-item info">
            <h3>当前页面信息</h3>
            <p><strong>当前URL:</strong> <span id="currentUrl"></span></p>
            <p><strong>基础路径:</strong> <span id="basePath"></span></p>
            <p><strong>协议:</strong> <span id="protocol"></span></p>
            <p><strong>主机:</strong> <span id="host"></span></p>
        </div>
        
        <div class="test-item">
            <h3>测试不同路径的customer.php</h3>
            <button onclick="testPath('customer.php')">测试 customer.php</button>
            <button onclick="testPath('mxj/customer.php')">测试 mxj/customer.php</button>
            <button onclick="testPath('/mxj/customer.php')">测试 /mxj/customer.php</button>
            <button onclick="testPath('creditmobile/customer.php')">测试 creditmobile/customer.php</button>
            <button onclick="testPath('/creditmobile/customer.php')">测试 /creditmobile/customer.php</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script src="/js/jquery-1.11.3.min.js"></script>
    <script>
        // 显示当前页面信息
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('basePath').textContent = window.location.pathname;
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('host').textContent = window.location.host;
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'test-item ' + type;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function testPath(path) {
            addResult(`<h4>测试路径: ${path}</h4><p>正在测试...</p>`, 'info');
            
            const startTime = Date.now();
            
            // 测试GET请求
            $.ajax({
                url: path + '?type=get_messages',
                type: 'GET',
                timeout: 5000,
                success: function(response, status, xhr) {
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    let message = `<h4>✅ ${path} - GET请求成功</h4>`;
                    message += `<p><strong>响应时间:</strong> ${duration}ms</p>`;
                    message += `<p><strong>状态码:</strong> ${xhr.status}</p>`;
                    message += `<p><strong>响应类型:</strong> ${typeof response}</p>`;
                    
                    if (typeof response === 'string') {
                        try {
                            const data = JSON.parse(response);
                            message += `<p><strong>JSON解析:</strong> 成功</p>`;
                            message += `<p><strong>响应代码:</strong> ${data.code}</p>`;
                            message += `<p><strong>响应消息:</strong> ${data.msg}</p>`;
                        } catch (e) {
                            message += `<p><strong>JSON解析:</strong> 失败 - ${e.message}</p>`;
                            message += `<p><strong>原始响应:</strong></p><pre>${response.substring(0, 500)}${response.length > 500 ? '...' : ''}</pre>`;
                        }
                    } else {
                        message += `<p><strong>响应数据:</strong></p><pre>${JSON.stringify(response, null, 2)}</pre>`;
                    }
                    
                    addResult(message, 'success');
                },
                error: function(xhr, status, error) {
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    let message = `<h4>❌ ${path} - GET请求失败</h4>`;
                    message += `<p><strong>响应时间:</strong> ${duration}ms</p>`;
                    message += `<p><strong>状态:</strong> ${status}</p>`;
                    message += `<p><strong>错误:</strong> ${error}</p>`;
                    message += `<p><strong>状态码:</strong> ${xhr.status}</p>`;
                    message += `<p><strong>响应文本:</strong></p><pre>${xhr.responseText || '无响应'}</pre>`;
                    
                    addResult(message, 'error');
                }
            });
            
            // 测试POST请求
            setTimeout(() => {
                $.ajax({
                    url: path + '?type=send_message',
                    type: 'POST',
                    data: {
                        message: '测试消息',
                        message_type: 'text'
                    },
                    timeout: 5000,
                    success: function(response, status, xhr) {
                        const endTime = Date.now();
                        
                        let message = `<h4>✅ ${path} - POST请求成功</h4>`;
                        message += `<p><strong>状态码:</strong> ${xhr.status}</p>`;
                        message += `<p><strong>响应类型:</strong> ${typeof response}</p>`;
                        
                        if (typeof response === 'string') {
                            try {
                                const data = JSON.parse(response);
                                message += `<p><strong>JSON解析:</strong> 成功</p>`;
                                message += `<p><strong>响应代码:</strong> ${data.code}</p>`;
                                message += `<p><strong>响应消息:</strong> ${data.msg}</p>`;
                            } catch (e) {
                                message += `<p><strong>JSON解析:</strong> 失败 - ${e.message}</p>`;
                                message += `<p><strong>原始响应:</strong></p><pre>${response.substring(0, 500)}${response.length > 500 ? '...' : ''}</pre>`;
                            }
                        } else {
                            message += `<p><strong>响应数据:</strong></p><pre>${JSON.stringify(response, null, 2)}</pre>`;
                        }
                        
                        addResult(message, 'success');
                    },
                    error: function(xhr, status, error) {
                        let message = `<h4>❌ ${path} - POST请求失败</h4>`;
                        message += `<p><strong>状态:</strong> ${status}</p>`;
                        message += `<p><strong>错误:</strong> ${error}</p>`;
                        message += `<p><strong>状态码:</strong> ${xhr.status}</p>`;
                        message += `<p><strong>响应文本:</strong></p><pre>${xhr.responseText || '无响应'}</pre>`;
                        
                        addResult(message, 'error');
                    }
                });
            }, 1000);
        }
        
        // 自动测试最可能的路径
        setTimeout(() => {
            addResult('<h3>开始自动测试...</h3>', 'info');
            testPath('mxj/customer.php');
        }, 1000);
    </script>
</body>
</html>
