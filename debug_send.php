<?php
// 调试发送消息功能
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>发送消息调试</h2>";

// 模拟登录状态
session_start();
if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = 'test_user';
    $_SESSION['userid'] = 1;
    echo "<p>⚠️ 模拟登录状态</p>";
}

include_once 'include/config.php';

$username = $_SESSION['username'];
$userid = $_SESSION['userid'];

echo "<p>用户名: $username</p>";
echo "<p>用户ID: $userid</p>";

// 检查数据库连接
if (isset($msql)) {
    echo "<p>✅ 数据库连接正常</p>";
} else {
    echo "<p>❌ 数据库连接失败</p>";
    exit;
}

// 检查表是否存在
$result = $msql->query("SHOW TABLES LIKE 'x_customer_messages'");
if ($msql->next_record()) {
    echo "<p>✅ x_customer_messages 表存在</p>";
} else {
    echo "<p>❌ x_customer_messages 表不存在</p>";
    
    // 创建表
    $create_sql = "CREATE TABLE IF NOT EXISTS `x_customer_messages` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `userid` int(11) NOT NULL,
        `username` varchar(50) NOT NULL,
        `message` text NOT NULL,
        `message_type` varchar(20) DEFAULT 'text',
        `image_url` varchar(255) DEFAULT '',
        `sender_type` varchar(20) NOT NULL,
        `is_read` tinyint(1) DEFAULT 0,
        `tjtime` datetime NOT NULL,
        `create_time` int(11) NOT NULL,
        PRIMARY KEY (`id`),
        KEY `userid` (`userid`),
        KEY `sender_type` (`sender_type`),
        KEY `create_time` (`create_time`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8";
    
    if ($msql->query($create_sql)) {
        echo "<p>✅ 成功创建 x_customer_messages 表</p>";
    } else {
        echo "<p>❌ 创建表失败: " . $msql->error() . "</p>";
    }
}

// 测试插入消息
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_message'])) {
    echo "<h3>测试发送消息</h3>";
    
    $message = trim($_POST['test_message']);
    $message_type = 'text';
    $image_url = '';
    $tjtime = date('Y-m-d H:i:s');
    $create_time = time();
    
    echo "<p>消息内容: $message</p>";
    echo "<p>发送时间: $tjtime</p>";
    
    $sql = "INSERT INTO x_customer_messages (userid, username, message, message_type, image_url, sender_type, tjtime, create_time) 
            VALUES ('$userid', '$username', '$message', '$message_type', '$image_url', 'user', '$tjtime', '$create_time')";
    
    echo "<p>SQL: $sql</p>";
    
    if ($msql->query($sql)) {
        $message_id = $msql->insert_id();
        echo "<p>✅ 消息插入成功，ID: $message_id</p>";
        
        // 测试JSON输出
        $result = [
            'code' => 1,
            'msg' => '发送成功',
            'data' => [
                'id' => $message_id,
                'message' => $message,
                'message_type' => $message_type,
                'image_url' => $image_url,
                'sender_type' => 'user',
                'tjtime' => $tjtime
            ]
        ];
        
        echo "<p>✅ JSON输出测试:</p>";
        echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT) . "</pre>";
        
    } else {
        echo "<p>❌ 消息插入失败: " . $msql->error() . "</p>";
    }
}

// 显示现有消息
echo "<h3>现有消息</h3>";
$msql->query("SELECT * FROM x_customer_messages WHERE userid='$userid' ORDER BY create_time DESC LIMIT 10");
if ($msql->num_rows() > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>消息</th><th>类型</th><th>发送者</th><th>时间</th></tr>";
    while ($msql->next_record()) {
        echo "<tr>";
        echo "<td>" . $msql->f('id') . "</td>";
        echo "<td>" . htmlspecialchars($msql->f('message')) . "</td>";
        echo "<td>" . $msql->f('message_type') . "</td>";
        echo "<td>" . $msql->f('sender_type') . "</td>";
        echo "<td>" . $msql->f('tjtime') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>暂无消息</p>";
}
?>

<form method="post">
    <h3>测试发送消息</h3>
    <textarea name="test_message" placeholder="输入测试消息" required></textarea><br>
    <button type="submit">发送测试消息</button>
</form>

<script>
// 测试AJAX发送
function testAjaxSend() {
    const message = document.getElementById('ajax_message').value;
    if (!message) {
        alert('请输入消息');
        return;
    }
    
    fetch('mxj/customer.php?type=send_message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message=' + encodeURIComponent(message) + '&message_type=text'
    })
    .then(response => response.text())
    .then(text => {
        console.log('原始响应:', text);
        try {
            const data = JSON.parse(text);
            console.log('解析后的数据:', data);
            alert('发送结果: ' + data.msg);
        } catch (e) {
            console.error('JSON解析错误:', e);
            alert('响应格式错误: ' + text);
        }
    })
    .catch(error => {
        console.error('请求错误:', error);
        alert('请求失败: ' + error);
    });
}
</script>

<h3>AJAX测试</h3>
<input type="text" id="ajax_message" placeholder="输入AJAX测试消息">
<button onclick="testAjaxSend()">AJAX发送测试</button>
