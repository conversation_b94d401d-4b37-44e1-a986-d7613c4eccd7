<?php
// 测试图片上传功能
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>图片上传测试</h2>";

// 检查上传目录
$uploadDir = 'uploads/customer/';
echo "<p>上传目录：$uploadDir</p>";
echo "<p>目录是否存在：" . (is_dir($uploadDir) ? '是' : '否') . "</p>";
echo "<p>目录是否可写：" . (is_writable($uploadDir) ? '是' : '否') . "</p>";

// 创建目录如果不存在
if (!is_dir($uploadDir)) {
    if (mkdir($uploadDir, 0777, true)) {
        echo "<p>✅ 成功创建目录</p>";
        chmod($uploadDir, 0777);
    } else {
        echo "<p>❌ 创建目录失败</p>";
    }
}

// 检查PHP配置
echo "<h3>PHP配置</h3>";
echo "<p>upload_max_filesize: " . ini_get('upload_max_filesize') . "</p>";
echo "<p>post_max_size: " . ini_get('post_max_size') . "</p>";
echo "<p>max_file_uploads: " . ini_get('max_file_uploads') . "</p>";
echo "<p>file_uploads: " . (ini_get('file_uploads') ? '启用' : '禁用') . "</p>";

// 处理上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_image'])) {
    echo "<h3>上传处理</h3>";
    
    $file = $_FILES['test_image'];
    echo "<p>文件信息：</p>";
    echo "<pre>" . print_r($file, true) . "</pre>";
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = date('YmdHis') . '_test.' . $extension;
        $filepath = $uploadDir . $filename;
        
        echo "<p>目标路径：$filepath</p>";
        
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            echo "<p>✅ 文件上传成功！</p>";
            echo "<p>文件大小：" . filesize($filepath) . " 字节</p>";
            echo "<p><img src='$filepath' style='max-width: 200px;'></p>";
        } else {
            echo "<p>❌ 文件移动失败</p>";
        }
    } else {
        echo "<p>❌ 上传错误：" . $file['error'] . "</p>";
    }
}
?>

<form method="post" enctype="multipart/form-data">
    <h3>测试上传</h3>
    <input type="file" name="test_image" accept="image/*" required>
    <button type="submit">上传测试</button>
</form>
