<?php
// 修复客服消息表结构
include('data/comm.inc.php');
include('data/mobivar.php');

echo "<h1>修复客服消息表结构</h1>";

// 检查表是否存在
$result = $msql->query("SHOW TABLES LIKE 'x_customer_messages'");
if (!$msql->next_record()) {
    echo "<p>❌ x_customer_messages 表不存在，正在创建...</p>";
    
    // 创建表
    $create_sql = "CREATE TABLE `x_customer_messages` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `userid` int(11) NOT NULL,
        `username` varchar(50) DEFAULT '',
        `message` text,
        `message_type` varchar(20) DEFAULT 'text',
        `image_url` varchar(255) DEFAULT '',
        `sender_type` varchar(20) DEFAULT 'user',
        `is_read` tinyint(1) DEFAULT 0,
        `create_time` int(11) NOT NULL,
        `tjtime` datetime NOT NULL,
        PRIMARY KEY (`id`),
        <PERSON><PERSON>Y `userid` (`userid`),
        <PERSON><PERSON>Y `sender_type` (`sender_type`),
        KEY `is_read` (`is_read`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8";
    
    if ($msql->query($create_sql)) {
        echo "<p>✅ 成功创建 x_customer_messages 表</p>";
    } else {
        echo "<p>❌ 创建表失败: " . $msql->error() . "</p>";
        exit;
    }
} else {
    echo "<p>✅ x_customer_messages 表已存在</p>";
    
    // 检查字段是否存在
    $result = $msql->query("SHOW COLUMNS FROM x_customer_messages LIKE 'username'");
    if (!$msql->next_record()) {
        echo "<p>⚠️ username 字段不存在，正在添加...</p>";
        
        $alter_sql = "ALTER TABLE x_customer_messages ADD COLUMN `username` varchar(50) DEFAULT '' AFTER `userid`";
        if ($msql->query($alter_sql)) {
            echo "<p>✅ 成功添加 username 字段</p>";
        } else {
            echo "<p>❌ 添加字段失败: " . $msql->error() . "</p>";
        }
    } else {
        echo "<p>✅ username 字段已存在</p>";
    }
}

// 显示表结构
echo "<h2>当前表结构</h2>";
$result = $msql->query("DESCRIBE x_customer_messages");
echo "<table border='1'>";
echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th><th>额外</th></tr>";
while ($msql->next_record()) {
    echo "<tr>";
    echo "<td>" . $msql->f('Field') . "</td>";
    echo "<td>" . $msql->f('Type') . "</td>";
    echo "<td>" . $msql->f('Null') . "</td>";
    echo "<td>" . $msql->f('Key') . "</td>";
    echo "<td>" . $msql->f('Default') . "</td>";
    echo "<td>" . $msql->f('Extra') . "</td>";
    echo "</tr>";
}
echo "</table>";

// 测试插入数据
echo "<h2>测试插入数据</h2>";
$test_userid = 1;
$test_username = 'test_user';
$test_message = '测试消息 ' . date('Y-m-d H:i:s');
$test_tjtime = date('Y-m-d H:i:s');
$test_create_time = time();

$insert_sql = "INSERT INTO x_customer_messages (userid, username, message, message_type, image_url, sender_type, is_read, create_time, tjtime) 
               VALUES ('$test_userid', '$test_username', '$test_message', 'text', '', 'user', 0, '$test_create_time', '$test_tjtime')";

echo "<p>SQL: $insert_sql</p>";

if ($msql->query($insert_sql)) {
    $message_id = $msql->insert_id();
    echo "<p>✅ 测试数据插入成功，ID: $message_id</p>";
    
    // 查询刚插入的数据
    $select_sql = "SELECT * FROM x_customer_messages WHERE id = '$message_id'";
    $msql->query($select_sql);
    if ($msql->next_record()) {
        echo "<p>✅ 数据查询成功:</p>";
        echo "<ul>";
        echo "<li>ID: " . $msql->f('id') . "</li>";
        echo "<li>用户ID: " . $msql->f('userid') . "</li>";
        echo "<li>用户名: " . $msql->f('username') . "</li>";
        echo "<li>消息: " . $msql->f('message') . "</li>";
        echo "<li>类型: " . $msql->f('message_type') . "</li>";
        echo "<li>发送者: " . $msql->f('sender_type') . "</li>";
        echo "<li>时间: " . $msql->f('tjtime') . "</li>";
        echo "</ul>";
    }
    
} else {
    echo "<p>❌ 测试数据插入失败: " . $msql->error() . "</p>";
}

echo "<p><a href='mxj/customer.php'>返回客服页面</a></p>";
?>
