<?php
// 完整的客服功能调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>客服功能完整调试</h1>";

// 检查会话
session_start();
echo "<h2>1. 会话检查</h2>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session 数据:</p>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

// 模拟登录状态（如果没有登录）
if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = 'test_user';
    $_SESSION['userid'] = 1;
    $_SESSION['uuid'] = 1;
    $_SESSION['ip'] = $_SERVER['REMOTE_ADDR'];
    $_SESSION['ucheck'] = 'test_check';
    $_SESSION['upasscode'] = 'test_pass';
    echo "<p>⚠️ 已模拟登录状态</p>";
}

// 检查数据库连接
echo "<h2>2. 数据库连接检查</h2>";
try {
    include_once 'data/comm.inc.php';
    include_once 'data/mobivar.php';
    
    if (isset($msql)) {
        echo "<p>✅ 数据库连接成功</p>";
        
        // 测试查询
        $test_query = "SELECT COUNT(*) as count FROM x_user";
        if ($msql->query($test_query)) {
            $msql->next_record();
            echo "<p>✅ 数据库查询正常，用户表记录数: " . $msql->f('count') . "</p>";
        } else {
            echo "<p>❌ 数据库查询失败</p>";
        }
    } else {
        echo "<p>❌ 数据库连接失败</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ 数据库连接异常: " . $e->getMessage() . "</p>";
}

// 检查用户表
echo "<h2>3. 用户信息检查</h2>";
$userid = $_SESSION['userid'];
$username = $_SESSION['username'];

$user_query = "SELECT * FROM x_user WHERE userid='$userid' LIMIT 1";
echo "<p>查询SQL: $user_query</p>";

if ($msql->query($user_query)) {
    if ($msql->next_record()) {
        echo "<p>✅ 用户信息查询成功:</p>";
        echo "<ul>";
        echo "<li>用户ID: " . $msql->f('userid') . "</li>";
        echo "<li>用户名: " . $msql->f('username') . "</li>";
        echo "<li>真实姓名: " . $msql->f('name') . "</li>";
        echo "<li>状态: " . $msql->f('status') . "</li>";
        echo "</ul>";
        $username = $msql->f('username'); // 使用数据库中的用户名
    } else {
        echo "<p>❌ 未找到用户信息</p>";
        
        // 创建测试用户
        $insert_user = "INSERT INTO x_user (userid, username, userpass, name, status, regtime) 
                       VALUES ('$userid', '$username', 'test123', '测试用户', 1, NOW())";
        if ($msql->query($insert_user)) {
            echo "<p>✅ 已创建测试用户</p>";
        } else {
            echo "<p>❌ 创建测试用户失败: " . $msql->error() . "</p>";
        }
    }
} else {
    echo "<p>❌ 用户查询失败: " . $msql->error() . "</p>";
}

// 检查客服消息表
echo "<h2>4. 客服消息表检查</h2>";
$table_check = "SHOW TABLES LIKE 'x_customer_messages'";
if ($msql->query($table_check)) {
    if ($msql->next_record()) {
        echo "<p>✅ x_customer_messages 表存在</p>";
        
        // 检查表结构
        $structure_query = "DESCRIBE x_customer_messages";
        if ($msql->query($structure_query)) {
            echo "<p>✅ 表结构:</p>";
            echo "<table border='1'>";
            echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th></tr>";
            while ($msql->next_record()) {
                echo "<tr>";
                echo "<td>" . $msql->f('Field') . "</td>";
                echo "<td>" . $msql->f('Type') . "</td>";
                echo "<td>" . $msql->f('Null') . "</td>";
                echo "<td>" . $msql->f('Key') . "</td>";
                echo "<td>" . $msql->f('Default') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p>❌ x_customer_messages 表不存在</p>";
    }
} else {
    echo "<p>❌ 表检查失败: " . $msql->error() . "</p>";
}

// 测试消息发送
echo "<h2>5. 消息发送测试</h2>";
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_send'])) {
    $test_message = trim($_POST['test_message']);
    $message_type = 'text';
    $image_url = '';
    $tjtime = date('Y-m-d H:i:s');
    $create_time = time();
    
    $insert_sql = "INSERT INTO x_customer_messages (userid, username, message, message_type, image_url, sender_type, is_read, create_time, tjtime) 
                   VALUES ('$userid', '$username', '$test_message', '$message_type', '$image_url', 'user', 0, '$create_time', '$tjtime')";
    
    echo "<p>插入SQL: $insert_sql</p>";
    
    if ($msql->query($insert_sql)) {
        $message_id = $msql->insert_id();
        echo "<p>✅ 消息插入成功，ID: $message_id</p>";
        
        // 构造返回数据
        $result = [
            'code' => 1,
            'msg' => '发送成功',
            'data' => [
                'id' => $message_id,
                'message' => $test_message,
                'message_type' => $message_type,
                'image_url' => $image_url,
                'sender_type' => 'user',
                'tjtime' => $tjtime,
                'create_time' => $create_time
            ]
        ];
        
        echo "<p>✅ 返回数据:</p>";
        echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        
    } else {
        echo "<p>❌ 消息插入失败: " . $msql->error() . "</p>";
    }
}

// 检查现有消息
echo "<h2>6. 现有消息列表</h2>";
$messages_query = "SELECT * FROM x_customer_messages WHERE userid='$userid' ORDER BY create_time DESC LIMIT 5";
if ($msql->query($messages_query)) {
    if ($msql->num_rows() > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>用户名</th><th>消息</th><th>类型</th><th>发送者</th><th>时间</th></tr>";
        while ($msql->next_record()) {
            echo "<tr>";
            echo "<td>" . $msql->f('id') . "</td>";
            echo "<td>" . $msql->f('username') . "</td>";
            echo "<td>" . htmlspecialchars($msql->f('message')) . "</td>";
            echo "<td>" . $msql->f('message_type') . "</td>";
            echo "<td>" . $msql->f('sender_type') . "</td>";
            echo "<td>" . $msql->f('tjtime') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>暂无消息记录</p>";
    }
} else {
    echo "<p>❌ 查询消息失败: " . $msql->error() . "</p>";
}

// 检查文件权限
echo "<h2>7. 文件和目录权限检查</h2>";
$files_to_check = [
    'mxj/customer.php',
    'templates/default/mxj/customer.html',
    'uploads/',
    'uploads/customer/',
    'js/dd.mp3'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "<p>✅ $file - 权限: " . substr(sprintf('%o', $perms), -4) . "</p>";
    } else {
        echo "<p>❌ $file - 文件不存在</p>";
    }
}
?>

<form method="post">
    <h3>测试发送消息</h3>
    <textarea name="test_message" placeholder="输入测试消息" required>测试消息 <?php echo date('Y-m-d H:i:s'); ?></textarea><br><br>
    <button type="submit" name="test_send">发送测试消息</button>
</form>

<script>
// AJAX测试
function testAjax() {
    console.log('开始AJAX测试...');
    
    const formData = new FormData();
    formData.append('message', '测试AJAX消息 ' + new Date().toLocaleString());
    formData.append('message_type', 'text');
    
    fetch('mxj/customer.php?type=send_message', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('响应状态:', response.status);
        console.log('响应头:', response.headers);
        return response.text();
    })
    .then(text => {
        console.log('原始响应:', text);
        document.getElementById('ajax-result').innerHTML = '<pre>' + text + '</pre>';
        
        try {
            const data = JSON.parse(text);
            console.log('解析后的数据:', data);
            alert('AJAX测试结果: ' + data.msg);
        } catch (e) {
            console.error('JSON解析错误:', e);
            alert('响应不是有效的JSON: ' + text);
        }
    })
    .catch(error => {
        console.error('请求错误:', error);
        alert('请求失败: ' + error);
    });
}
</script>

<h3>AJAX测试</h3>
<button onclick="testAjax()">测试AJAX发送</button>
<div id="ajax-result"></div>

<p><a href="mxj/customer.php">打开客服页面</a></p>
