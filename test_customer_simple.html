<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .input-group {
            position: relative;
            margin: 20px 0;
        }
        
        .input-tools {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .tool-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 6px;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.2s;
        }
        
        .tool-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: scale(1.05);
        }
        
        .emoji-panel {
            position: absolute;
            bottom: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .emoji-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        
        .emoji-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #999;
        }
        
        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 5px;
        }
        
        .emoji-item {
            padding: 8px;
            text-align: center;
            cursor: pointer;
            border-radius: 4px;
            font-size: 18px;
            transition: background 0.2s;
        }
        
        .emoji-item:hover {
            background: #f0f0f0;
        }
        
        .input-text {
            width: 100%;
            min-height: 40px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            font-size: 14px;
        }
        
        .send-btn {
            margin-top: 10px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .send-btn:hover {
            background: #5a6fd8;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 6px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>客服功能测试</h1>
        
        <div class="input-group">
            <!-- 表情包面板 -->
            <div class="emoji-panel" id="emojiPanel" style="display: none;">
                <div class="emoji-header">
                    <span class="emoji-title">选择表情</span>
                    <button class="emoji-close" onclick="toggleEmojiPanel()">×</button>
                </div>
                <div class="emoji-grid">
                    <span class="emoji-item" onclick="insertEmoji('😀')">😀</span>
                    <span class="emoji-item" onclick="insertEmoji('😃')">😃</span>
                    <span class="emoji-item" onclick="insertEmoji('😄')">😄</span>
                    <span class="emoji-item" onclick="insertEmoji('😁')">😁</span>
                    <span class="emoji-item" onclick="insertEmoji('😆')">😆</span>
                    <span class="emoji-item" onclick="insertEmoji('😅')">😅</span>
                    <span class="emoji-item" onclick="insertEmoji('😂')">😂</span>
                    <span class="emoji-item" onclick="insertEmoji('🤣')">🤣</span>
                    <span class="emoji-item" onclick="insertEmoji('😊')">😊</span>
                    <span class="emoji-item" onclick="insertEmoji('😇')">😇</span>
                    <span class="emoji-item" onclick="insertEmoji('🙂')">🙂</span>
                    <span class="emoji-item" onclick="insertEmoji('🙃')">🙃</span>
                    <span class="emoji-item" onclick="insertEmoji('😉')">😉</span>
                    <span class="emoji-item" onclick="insertEmoji('😌')">😌</span>
                    <span class="emoji-item" onclick="insertEmoji('😍')">😍</span>
                    <span class="emoji-item" onclick="insertEmoji('🥰')">🥰</span>
                </div>
            </div>
            
            <div class="input-tools">
                <button class="tool-btn" onclick="toggleEmojiPanel()" title="表情">
                    😀
                </button>
                <button class="tool-btn" onclick="triggerImageUpload()" title="上传图片">
                    📷
                </button>
                <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
            </div>
            
            <textarea class="input-text" id="messageInput" placeholder="请输入您的问题..." rows="3"></textarea>
            <button class="send-btn" id="sendBtn" onclick="sendMessage()">发送消息</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script src="/js/jquery-1.11.3.min.js"></script>
    <script>
        // 全局变量
        let isLoading = false;
        
        // 表情包面板切换
        function toggleEmojiPanel() {
            console.log('toggleEmojiPanel 被调用');
            const panel = document.getElementById('emojiPanel');
            if (panel.style.display === 'none' || panel.style.display === '') {
                panel.style.display = 'block';
                console.log('表情面板已打开');
            } else {
                panel.style.display = 'none';
                console.log('表情面板已关闭');
            }
        }
        
        // 插入表情
        function insertEmoji(emoji) {
            console.log('插入表情:', emoji);
            const input = document.getElementById('messageInput');
            const cursorPos = input.selectionStart;
            const textBefore = input.value.substring(0, cursorPos);
            const textAfter = input.value.substring(cursorPos);
            
            input.value = textBefore + emoji + textAfter;
            input.focus();
            input.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
            
            // 关闭表情面板
            document.getElementById('emojiPanel').style.display = 'none';
        }
        
        // 触发图片上传
        function triggerImageUpload() {
            console.log('触发图片上传');
            document.getElementById('imageInput').click();
        }
        
        // 处理图片上传
        function handleImageUpload(event) {
            console.log('处理图片上传');
            const file = event.target.files[0];
            if (!file) return;
            
            showResult('已选择图片: ' + file.name, 'success');
        }
        
        // 发送消息
        function sendMessage() {
            console.log('sendMessage 被调用');
            const messageText = document.getElementById('messageInput').value.trim();
            
            if (!messageText) {
                showResult('请输入消息内容', 'error');
                return;
            }
            
            if (isLoading) {
                showResult('正在发送中，请稍候...', 'error');
                return;
            }
            
            isLoading = true;
            document.getElementById('sendBtn').disabled = true;
            
            showResult('正在发送消息...', '');
            
            // 使用jQuery发送AJAX请求
            $.post('mxj/customer.php?type=send_message', {
                message: messageText,
                message_type: 'text'
            })
            .done(function(response) {
                console.log('服务器响应:', response);
                
                try {
                    let data;
                    if (typeof response === 'string') {
                        data = JSON.parse(response);
                    } else {
                        data = response;
                    }
                    
                    if (data.code === 1) {
                        showResult('消息发送成功！', 'success');
                        document.getElementById('messageInput').value = '';
                    } else {
                        showResult('发送失败: ' + (data.msg || '未知错误'), 'error');
                    }
                } catch (e) {
                    console.error('JSON解析错误:', e);
                    showResult('服务器响应格式错误: ' + response, 'error');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('请求失败:', status, error);
                showResult('网络请求失败: ' + error, 'error');
            })
            .always(function() {
                isLoading = false;
                document.getElementById('sendBtn').disabled = false;
            });
        }
        
        // 显示结果
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = 'result ' + type;
            result.style.display = 'block';
        }
        
        // 点击空白处关闭表情面板
        document.addEventListener('click', function(e) {
            const emojiPanel = document.getElementById('emojiPanel');
            const emojiBtn = e.target.closest('.tool-btn');
            
            if (emojiPanel && emojiPanel.style.display === 'block' && !emojiPanel.contains(e.target) && !emojiBtn) {
                emojiPanel.style.display = 'none';
            }
        });
        
        // 回车发送
        document.getElementById('messageInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        console.log('测试页面已加载完成');
    </script>
</body>
</html>
