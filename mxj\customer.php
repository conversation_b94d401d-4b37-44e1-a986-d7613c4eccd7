<?php

include('../data/comm.inc.php');
include('../data/mobivar.php');
include('../func/func.php');
include('../func/csfunc.php');

include('../func/userfunc.php');
include('../include.php');
include('./checklogin.php');

$type = $_GET['type'];

// 检查并创建客服消息表
$check_table = "SHOW TABLES LIKE 'x_customer_messages'";
if ($msql->query($check_table)) {
    if (!$msql->next_record()) {
        // 表不存在，创建表
        $create_table = "CREATE TABLE `x_customer_messages` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `userid` int(11) NOT NULL,
            `username` varchar(50) DEFAULT '',
            `message` text,
            `message_type` varchar(20) DEFAULT 'text',
            `image_url` varchar(255) DEFAULT '',
            `sender_type` varchar(20) DEFAULT 'user',
            `is_read` tinyint(1) DEFAULT 0,
            `create_time` int(11) NOT NULL,
            `tjtime` datetime NOT NULL,
            PRIMARY KEY (`id`),
            KEY `userid` (`userid`),
            KEY `sender_type` (`sender_type`),
            KEY `is_read` (`is_read`)
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8";
        $msql->query($create_table);
    }
}

// 获取用户信息
$msql->query("select username, kmoney from `$tb_user` where userid='$userid'");
$msql->next_record();
$username = $msql->f('username');
$user_balance = $msql->f('kmoney');

// 处理AJAX请求
if (in_array($type, ['send_message', 'upload_image', 'check_new_messages', 'get_unread_count', 'get_messages', 'get_contact_info'])) {
    // 设置JSON响应头
    header('Content-Type: application/json; charset=utf-8');
}

switch ($type) {
    case 'send_message':
        // 发送消息
        $result = ['code' => 1, 'msg' => '发送成功'];
        try {
            if(!$userid) throw new Exception('请先登录');
            
            $message = strip_tags($_POST['message']);
            $message_type = strip_tags($_POST['message_type']); // text, image
            $image_url = strip_tags($_POST['image_url']);
            
            if(empty($message) && empty($image_url)) {
                throw new Exception('消息内容不能为空');
            }
            
            $time = time();
            $tjtime = date('Y-m-d H:i:s');
            
            // 插入消息记录
            $sql = "INSERT INTO x_customer_messages SET
                userid='$userid',
                username='$username',
                message='$message',
                message_type='$message_type',
                image_url='$image_url',
                sender_type='user',
                is_read=0,
                create_time='$time',
                tjtime='$tjtime'";
                
            if (!$msql->query($sql)) throw new Exception('发送失败，请重试');
            
            $message_id = $msql->insert_id();
            
            // 返回消息信息
            $result['data'] = array(
                'id' => $message_id,
                'message' => $message,
                'message_type' => $message_type,
                'image_url' => $image_url,
                'sender_type' => 'user',
                'tjtime' => $tjtime
            );
            
        } catch (Exception $e) {
            $result['code'] = 0;
            $result['msg'] = $e->getMessage();
        }
        exit(json_encode($result));
        break;

    case 'upload_image':
        // 图片上传
        $result = ['code' => 1, 'msg' => '上传成功'];
        try {
            if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
                $error_msg = '图片上传失败';
                if (isset($_FILES['image']['error'])) {
                    switch ($_FILES['image']['error']) {
                        case UPLOAD_ERR_INI_SIZE:
                            $error_msg = '文件大小超过了 php.ini 中 upload_max_filesize 选项限制的值';
                            break;
                        case UPLOAD_ERR_FORM_SIZE:
                            $error_msg = '文件大小超过了 HTML 表单中 MAX_FILE_SIZE 选项指定的值';
                            break;
                        case UPLOAD_ERR_PARTIAL:
                            $error_msg = '文件只有部分被上传';
                            break;
                        case UPLOAD_ERR_NO_FILE:
                            $error_msg = '没有文件被上传';
                            break;
                        case UPLOAD_ERR_NO_TMP_DIR:
                            $error_msg = '找不到临时文件夹';
                            break;
                        case UPLOAD_ERR_CANT_WRITE:
                            $error_msg = '文件写入失败';
                            break;
                    }
                }
                throw new Exception($error_msg);
            }

            $file = $_FILES['image'];
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

            if (!in_array($file['type'], $allowedTypes)) {
                throw new Exception('不支持的图片格式：' . $file['type']);
            }

            if ($file['size'] > 5 * 1024 * 1024) { // 5MB
                throw new Exception('图片大小不能超过5MB，当前大小：' . round($file['size'] / 1024 / 1024, 2) . 'MB');
            }

            // 创建上传目录
            $uploadDir = '../uploads/customer/';

            if (!is_dir($uploadDir)) {
                if (!mkdir($uploadDir, 0777, true)) {
                    throw new Exception('无法创建上传目录：' . $uploadDir);
                }
                chmod($uploadDir, 0777);
            }

            // 检查目录权限
            if (!is_writable($uploadDir)) {
                throw new Exception('上传目录不可写：' . $uploadDir);
            }

            // 生成文件名
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = date('YmdHis') . '_' . uniqid() . '.' . $extension;
            $filepath = $uploadDir . $filename;

            // 检查临时文件是否存在
            if (!file_exists($file['tmp_name'])) {
                throw new Exception('临时文件不存在：' . $file['tmp_name']);
            }

            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new Exception('文件保存失败，源：' . $file['tmp_name'] . '，目标：' . $filepath);
            }

            // 验证文件是否成功保存
            if (!file_exists($filepath)) {
                throw new Exception('文件保存后验证失败');
            }

            $result['data'] = [
                'image_url' => '/uploads/customer/' . $filename,
                'filename' => $filename
            ];

        } catch (Exception $e) {
            $result['code'] = 0;
            $result['msg'] = $e->getMessage();
        }
        exit(json_encode($result));
        break;

    case 'check_new_messages':
        // 检查新消息
        $last_id = intval($_GET['last_id']);
        $msql->query("SELECT * FROM x_customer_messages WHERE userid='$userid' AND id > '$last_id' ORDER BY create_time ASC");
        $messages = array();
        while($msql->next_record()) {
            $messages[] = array(
                'id' => $msql->f('id'),
                'message' => $msql->f('message'),
                'message_type' => $msql->f('message_type'),
                'image_url' => $msql->f('image_url'),
                'sender_type' => $msql->f('sender_type'),
                'is_read' => $msql->f('is_read'),
                'tjtime' => $msql->f('tjtime'),
                'create_time' => $msql->f('create_time')
            );
        }

        // 标记用户收到的管理员消息为已读
        if(!empty($messages)) {
            $msql->query("UPDATE x_customer_messages SET is_read=1 WHERE userid='$userid' AND sender_type='admin' AND is_read=0");
        }

        exit(json_encode(['code' => 1, 'data' => $messages]));
        break;

    case 'get_unread_count':
        // 获取未读消息数量
        $msql->query("SELECT COUNT(*) as count FROM x_customer_messages WHERE userid='$userid' AND sender_type='admin' AND is_read=0");
        $msql->next_record();
        $count = $msql->f('count');

        exit(json_encode(['code' => 1, 'data' => ['count' => $count]]));
        break;

    case 'get_messages':
        // 获取消息列表
        $page = intval($_GET['page']) ?: 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        // 获取消息
        $msql->query("SELECT * FROM x_customer_messages WHERE userid='$userid' ORDER BY create_time DESC LIMIT $offset, $limit");
        $messages = array();
        while($msql->next_record()) {
            $messages[] = array(
                'id' => $msql->f('id'),
                'message' => $msql->f('message'),
                'message_type' => $msql->f('message_type'),
                'image_url' => $msql->f('image_url'),
                'sender_type' => $msql->f('sender_type'),
                'is_read' => $msql->f('is_read'),
                'tjtime' => $msql->f('tjtime'),
                'create_time' => $msql->f('create_time')
            );
        }
        
        // 反转数组，让最新的消息在底部
        $messages = array_reverse($messages);
        
        // 标记用户消息为已读
        $msql->query("UPDATE x_customer_messages SET is_read=1 WHERE userid='$userid' AND sender_type='admin' AND is_read=0");
        
        exit(json_encode(['code' => 1, 'data' => $messages]));
        break;
        
    case 'get_unread_count':
        // 获取未读消息数量
        $msql->query("SELECT COUNT(*) as count FROM x_customer_messages WHERE userid='$userid' AND sender_type='admin' AND is_read=0");
        $msql->next_record();
        $count = $msql->f('count');
        
        exit(json_encode(['code' => 1, 'data' => ['count' => intval($count)]]));
        break;
        
    case 'get_contact_info':
        // 获取联系方式
        $msql->query("SELECT config_key, config_value FROM x_customer_config WHERE is_enabled=1");
        $contact_info = array();
        while($msql->next_record()) {
            $contact_info[$msql->f('config_key')] = $msql->f('config_value');
        }
        
        exit(json_encode(['code' => 1, 'data' => $contact_info]));
        break;
        
    default:
        // 获取用户信息
        $msql->query("select username, kmoney from `$tb_user` where userid='$userid'");
        $msql->next_record();
        $username = $msql->f('username');
        $user_balance = $msql->f('kmoney');
        
        // 获取联系方式配置
        $msql->query("SELECT config_key, config_value FROM x_customer_config WHERE is_enabled=1");
        $contact_info = array();
        while($msql->next_record()) {
            $contact_info[$msql->f('config_key')] = $msql->f('config_value');
        }
        
        // 获取未读消息数量
        $msql->query("SELECT COUNT(*) as count FROM x_customer_messages WHERE userid='$userid' AND sender_type='admin' AND is_read=0");
        $msql->next_record();
        $unread_count = $msql->f('count');
        
        $tpl->assign('username', $username);
        $tpl->assign('user_balance', $user_balance);
        $tpl->assign('contact_info', $contact_info);
        $tpl->assign('unread_count', $unread_count);
        $tpl->display("customer.html");
}
