<?php

include('../data/comm.inc.php');
include('../data/mobivar.php');
include('../func/func.php');
include('../func/csfunc.php');

include('../func/userfunc.php');
include('../include.php');
include('./checklogin.php');

$type = $_GET['type'];
switch ($type) {
    case 'send_message':
        // 发送消息
        $result = ['code' => 1, 'msg' => '发送成功'];
        try {
            if(!$userid) throw new Exception('请先登录');
            
            $message = strip_tags($_POST['message']);
            $message_type = strip_tags($_POST['message_type']); // text, image
            $image_url = strip_tags($_POST['image_url']);
            
            if(empty($message) && empty($image_url)) {
                throw new Exception('消息内容不能为空');
            }
            
            $time = time();
            $tjtime = date('Y-m-d H:i:s');
            
            // 插入消息记录
            $sql = "INSERT INTO x_customer_messages SET 
                userid='$userid',
                message='$message',
                message_type='$message_type',
                image_url='$image_url',
                sender_type='user',
                is_read=0,
                create_time='$time',
                tjtime='$tjtime'";
                
            if (!$msql->query($sql)) throw new Exception('发送失败，请重试');
            
            $message_id = $msql->insert_id();
            
            // 返回消息信息
            $result['data'] = array(
                'id' => $message_id,
                'message' => $message,
                'message_type' => $message_type,
                'image_url' => $image_url,
                'sender_type' => 'user',
                'tjtime' => $tjtime
            );
            
        } catch (Exception $e) {
            $result['code'] = 0;
            $result['msg'] = $e->getMessage();
        }
        exit(json_encode($result));
        break;

    case 'upload_image':
        // 图片上传
        $result = ['code' => 1, 'msg' => '上传成功'];
        try {
            if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('图片上传失败');
            }

            $file = $_FILES['image'];
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

            if (!in_array($file['type'], $allowedTypes)) {
                throw new Exception('不支持的图片格式');
            }

            if ($file['size'] > 5 * 1024 * 1024) { // 5MB
                throw new Exception('图片大小不能超过5MB');
            }

            // 创建上传目录
            $uploadDir = '../uploads/customer/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // 生成文件名
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = date('YmdHis') . '_' . uniqid() . '.' . $extension;
            $filepath = $uploadDir . $filename;

            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new Exception('文件保存失败');
            }

            $result['data'] = [
                'image_url' => '/uploads/customer/' . $filename,
                'filename' => $filename
            ];

        } catch (Exception $e) {
            $result['code'] = 0;
            $result['msg'] = $e->getMessage();
        }
        exit(json_encode($result));
        break;

    case 'check_new_messages':
        // 检查新消息
        $last_id = intval($_GET['last_id']);
        $msql->query("SELECT * FROM x_customer_messages WHERE userid='$userid' AND id > '$last_id' ORDER BY create_time ASC");
        $messages = array();
        while($msql->next_record()) {
            $messages[] = array(
                'id' => $msql->f('id'),
                'message' => $msql->f('message'),
                'message_type' => $msql->f('message_type'),
                'image_url' => $msql->f('image_url'),
                'sender_type' => $msql->f('sender_type'),
                'is_read' => $msql->f('is_read'),
                'tjtime' => $msql->f('tjtime'),
                'create_time' => $msql->f('create_time')
            );
        }

        // 标记用户收到的管理员消息为已读
        if(!empty($messages)) {
            $msql->query("UPDATE x_customer_messages SET is_read=1 WHERE userid='$userid' AND sender_type='admin' AND is_read=0");
        }

        exit(json_encode(['code' => 1, 'data' => $messages]));
        break;

    case 'get_unread_count':
        // 获取未读消息数量
        $msql->query("SELECT COUNT(*) as count FROM x_customer_messages WHERE userid='$userid' AND sender_type='admin' AND is_read=0");
        $msql->next_record();
        $count = $msql->f('count');

        exit(json_encode(['code' => 1, 'data' => ['count' => $count]]));
        break;

    case 'get_messages':
        // 获取消息列表
        $page = intval($_GET['page']) ?: 1;
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        // 获取消息
        $msql->query("SELECT * FROM x_customer_messages WHERE userid='$userid' ORDER BY create_time DESC LIMIT $offset, $limit");
        $messages = array();
        while($msql->next_record()) {
            $messages[] = array(
                'id' => $msql->f('id'),
                'message' => $msql->f('message'),
                'message_type' => $msql->f('message_type'),
                'image_url' => $msql->f('image_url'),
                'sender_type' => $msql->f('sender_type'),
                'is_read' => $msql->f('is_read'),
                'tjtime' => $msql->f('tjtime'),
                'create_time' => $msql->f('create_time')
            );
        }
        
        // 反转数组，让最新的消息在底部
        $messages = array_reverse($messages);
        
        // 标记用户消息为已读
        $msql->query("UPDATE x_customer_messages SET is_read=1 WHERE userid='$userid' AND sender_type='admin' AND is_read=0");
        
        exit(json_encode(['code' => 1, 'data' => $messages]));
        break;
        
    case 'get_unread_count':
        // 获取未读消息数量
        $msql->query("SELECT COUNT(*) as count FROM x_customer_messages WHERE userid='$userid' AND sender_type='admin' AND is_read=0");
        $msql->next_record();
        $count = $msql->f('count');
        
        exit(json_encode(['code' => 1, 'data' => ['count' => intval($count)]]));
        break;
        
    case 'get_contact_info':
        // 获取联系方式
        $msql->query("SELECT config_key, config_value FROM x_customer_config WHERE is_enabled=1");
        $contact_info = array();
        while($msql->next_record()) {
            $contact_info[$msql->f('config_key')] = $msql->f('config_value');
        }
        
        exit(json_encode(['code' => 1, 'data' => $contact_info]));
        break;
        
    default:
        // 获取用户信息
        $msql->query("select username, kmoney from `$tb_user` where userid='$userid'");
        $msql->next_record();
        $username = $msql->f('username');
        $user_balance = $msql->f('kmoney');
        
        // 获取联系方式配置
        $msql->query("SELECT config_key, config_value FROM x_customer_config WHERE is_enabled=1");
        $contact_info = array();
        while($msql->next_record()) {
            $contact_info[$msql->f('config_key')] = $msql->f('config_value');
        }
        
        // 获取未读消息数量
        $msql->query("SELECT COUNT(*) as count FROM x_customer_messages WHERE userid='$userid' AND sender_type='admin' AND is_read=0");
        $msql->next_record();
        $unread_count = $msql->f('count');
        
        $tpl->assign('username', $username);
        $tpl->assign('user_balance', $user_balance);
        $tpl->assign('contact_info', $contact_info);
        $tpl->assign('unread_count', $unread_count);
        $tpl->display("customer.html");
}
