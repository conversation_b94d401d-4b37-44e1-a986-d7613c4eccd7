<?php
// 创建客服系统数据库表

include('../data/comm.inc.php');
include('../data/mobivar.php');

// 创建客服消息表
$create_messages_table = "CREATE TABLE IF NOT EXISTS `x_customer_messages` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `userid` int(11) NOT NULL,
    `username` varchar(50) DEFAULT '',
    `message` text,
    `message_type` varchar(20) DEFAULT 'text',
    `image_url` varchar(255) DEFAULT '',
    `sender_type` varchar(20) DEFAULT 'user',
    `is_read` tinyint(1) DEFAULT 0,
    `create_time` int(11) NOT NULL,
    `tjtime` datetime NOT NULL,
    PRIMARY KEY (`id`),
    KEY `userid` (`userid`),
    KEY `sender_type` (`sender_type`),
    KEY `is_read` (`is_read`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8";

// 创建客服配置表
$create_config_table = "CREATE TABLE IF NOT EXISTS `x_customer_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_key` varchar(50) NOT NULL,
    `config_value` text,
    `config_name` varchar(100) DEFAULT '',
    `is_enabled` tinyint(1) DEFAULT 1,
    `sort_order` int(11) DEFAULT 0,
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_key` (`config_key`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8";

echo "<h1>创建客服系统数据库表</h1>";

// 执行创建消息表
if ($msql->query($create_messages_table)) {
    echo "<p>✅ 客服消息表创建成功</p>";
} else {
    echo "<p>❌ 客服消息表创建失败: " . $msql->error() . "</p>";
}

// 执行创建配置表
if ($msql->query($create_config_table)) {
    echo "<p>✅ 客服配置表创建成功</p>";
} else {
    echo "<p>❌ 客服配置表创建失败: " . $msql->error() . "</p>";
}

// 插入默认配置
$default_configs = array(
    array('kfqq', '123456789', 'QQ客服'),
    array('kfwx', 'customer_service', '微信客服'),
    array('kftg', '@customer_service', 'Telegram客服'),
    array('kfemail', '<EMAIL>', '邮箱客服'),
    array('kfphone', '************', '电话客服')
);

echo "<h2>插入默认配置</h2>";
foreach($default_configs as $config) {
    $sql = "INSERT IGNORE INTO x_customer_config (config_key, config_value, config_name, is_enabled) 
            VALUES ('{$config[0]}', '{$config[1]}', '{$config[2]}', 1)";
    if ($msql->query($sql)) {
        echo "<p>✅ 配置 {$config[2]} 插入成功</p>";
    } else {
        echo "<p>❌ 配置 {$config[2]} 插入失败: " . $msql->error() . "</p>";
    }
}

echo "<h2>测试数据库连接</h2>";
$test_sql = "SELECT COUNT(*) as count FROM x_customer_messages";
if ($msql->query($test_sql)) {
    $msql->next_record();
    $count = $msql->f('count');
    echo "<p>✅ 数据库连接正常，当前消息数量: $count</p>";
} else {
    echo "<p>❌ 数据库连接测试失败: " . $msql->error() . "</p>";
}

echo "<p><a href='customer.php'>返回客服页面</a></p>";
?>
