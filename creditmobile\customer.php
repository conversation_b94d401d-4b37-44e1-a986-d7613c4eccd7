<?php
session_start();
include_once '../include/config.php';

// 检查用户是否登录
if (!isset($_SESSION['username']) || empty($_SESSION['username'])) {
    header("Location: /creditmobile/login");
    exit;
}

$username = $_SESSION['username'];
$userid = $_SESSION['userid'];

// 获取用户信息
$msql->query("SELECT * FROM x_user WHERE username='$username' LIMIT 1");
$msql->next_record();
$userinfo = array(
    'username' => $msql->f('username'),
    'userid' => $msql->f('userid'),
    'money' => $msql->f('money'),
    'status' => $msql->f('status')
);

// 处理AJAX请求
if (isset($_GET['type'])) {
    $type = $_GET['type'];
    
    switch ($type) {
        case 'get_messages':
            // 获取消息列表
            $msql->query("SELECT * FROM x_customer_messages WHERE userid='$userid' ORDER BY create_time DESC LIMIT 50");
            $messages = array();
            while ($msql->next_record()) {
                $messages[] = array(
                    'id' => $msql->f('id'),
                    'message' => $msql->f('message'),
                    'message_type' => $msql->f('message_type'),
                    'image_url' => $msql->f('image_url'),
                    'sender_type' => $msql->f('sender_type'),
                    'tjtime' => $msql->f('tjtime'),
                    'create_time' => $msql->f('create_time')
                );
            }
            
            $result = array('code' => 1, 'data' => $messages);
            exit(json_encode($result));
            break;
            
        case 'send_message':
            // 发送消息
            $message = trim($_POST['message']);
            $message_type = isset($_POST['message_type']) ? $_POST['message_type'] : 'text';
            $image_url = isset($_POST['image_url']) ? $_POST['image_url'] : '';
            
            if (empty($message) && empty($image_url)) {
                $result = array('code' => 0, 'msg' => '消息内容不能为空');
                exit(json_encode($result));
            }
            
            $tjtime = date('Y-m-d H:i:s');
            $create_time = time();
            
            $sql = "INSERT INTO x_customer_messages (userid, username, message, message_type, image_url, sender_type, tjtime, create_time)
                    VALUES ('$userid', '$username', '$message', '$message_type', '$image_url', 'user', '$tjtime', '$create_time')";

            if ($msql->query($sql)) {
                $message_id = $msql->insert_id();
                $result = array(
                    'code' => 1,
                    'msg' => '发送成功',
                    'data' => array(
                        'id' => $message_id,
                        'message' => $message,
                        'message_type' => $message_type,
                        'image_url' => $image_url,
                        'sender_type' => 'user',
                        'tjtime' => $tjtime,
                        'create_time' => $create_time
                    )
                );
            } else {
                $result = array('code' => 0, 'msg' => '发送失败');
            }
            exit(json_encode($result));
            break;
            
        case 'upload_image':
            // 图片上传
            $result = ['code' => 1, 'msg' => '上传成功'];
            try {
                if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
                    throw new Exception('图片上传失败');
                }
                
                $file = $_FILES['image'];
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                
                if (!in_array($file['type'], $allowedTypes)) {
                    throw new Exception('不支持的图片格式');
                }
                
                if ($file['size'] > 5 * 1024 * 1024) { // 5MB
                    throw new Exception('图片大小不能超过5MB');
                }
                
                // 创建上传目录
                $uploadDir = '../uploads/customer/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                // 生成文件名
                $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                $filename = date('YmdHis') . '_' . uniqid() . '.' . $extension;
                $filepath = $uploadDir . $filename;
                
                if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                    throw new Exception('文件保存失败');
                }
                
                $result['data'] = [
                    'image_url' => '/uploads/customer/' . $filename,
                    'filename' => $filename
                ];
                
            } catch (Exception $e) {
                $result['code'] = 0;
                $result['msg'] = $e->getMessage();
            }
            exit(json_encode($result));
            break;
            
        case 'check_new_messages':
            // 检查新消息
            $last_id = intval($_GET['last_id']);
            $msql->query("SELECT * FROM x_customer_messages WHERE userid='$userid' AND id > '$last_id' ORDER BY create_time ASC");
            $messages = array();
            while ($msql->next_record()) {
                $messages[] = array(
                    'id' => $msql->f('id'),
                    'message' => $msql->f('message'),
                    'message_type' => $msql->f('message_type'),
                    'image_url' => $msql->f('image_url'),
                    'sender_type' => $msql->f('sender_type'),
                    'tjtime' => $msql->f('tjtime'),
                    'create_time' => $msql->f('create_time')
                );
            }
            
            $result = array('code' => 1, 'data' => $messages);
            exit(json_encode($result));
            break;
    }
}

// 获取网站配置
$msql->query("SELECT * FROM x_config WHERE id=1");
$msql->next_record();
$config = array(
    'webname' => $msql->f('webname'),
    'kfqq' => $msql->f('kfqq'),
    'kfwx' => $msql->f('kfwx'),
    'kftg' => $msql->f('kftg'),
    'kfemail' => $msql->f('kfemail'),
    'kfphone' => $msql->f('kfphone')
);

// 模板变量
$smarty->assign('username', $username);
$smarty->assign('userid', $userid);
$smarty->assign('userinfo', $userinfo);
$smarty->assign('config', $config);
$smarty->assign('title', '在线客服 - ' . $config['webname']);

// 显示模板
$smarty->display('customer_mobile.html');
?>
